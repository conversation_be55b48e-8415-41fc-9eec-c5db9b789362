/**
 * Utility functions to convert between different text formats for TipTap editor
 */

/**
 * Convert markdown-like text to HTML for the editor
 */
export const convertToHtml = (text: string): string => {
  // First, handle code blocks to protect them from other replacements
  let html = text.replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>');
  
  // Handle headings
  html = html
    .replace(/^### (.*$)/gm, '<h3>$1</h3>')
    .replace(/^## (.*$)/gm, '<h2>$1</h2>')
    .replace(/^# (.*$)/gm, '<h1>$1</h1>');
  
  // Handle markdown tables by converting them to HTML
  html = html.replace(/^\|(.*)\|\s*\n\|[-\s|:]+\|\s*\n((?:\|.*\|\s*\n?)*)/gm, (match, headerRow, bodyRows) => {
    // Parse headers - the regex already removes the outer pipes, so split normally
    const headers = headerRow.split('|').map(cell => cell.trim());
    
    const bodyLines = bodyRows.trim().split('\n').filter(line => line.trim().length > 0);
    const rows = bodyLines.map(line => {
      // Remove the outer pipes and split by inner pipes
      const cleanLine = line.trim().replace(/^\||\|$/g, '');
      return cleanLine.split('|').map(cell => cell.trim());
    });
    
    // Build HTML table structure preserving ALL columns including empty ones
    let htmlTable = '<table>';
    
    // Add header preserving ALL columns including empty ones
    if (headers.length > 0) {
      htmlTable += '<thead><tr>';
      headers.forEach(header => {
        htmlTable += `<th><p>${header}</p></th>`;
      });
      htmlTable += '</tr></thead>';
    }
    
    // Add body rows preserving ALL columns including empty ones
    if (rows.length > 0) {
      htmlTable += '<tbody>';
      rows.forEach(row => {
        htmlTable += '<tr>';
        // Ensure each row has the same number of columns as the header
        const maxCols = Math.max(headers.length, row.length);
        for (let i = 0; i < maxCols; i++) {
          const cellContent = i < row.length ? row[i] : '';
          htmlTable += `<td><p>${cellContent}</p></td>`;
        }
        htmlTable += '</tr>';
      });
      htmlTable += '</tbody>';
    }
    
    htmlTable += '</table>';
    return htmlTable;
  });
  
  // Handle lists by grouping consecutive items
  const lines = html.split('\n');
  const processedLines: string[] = [];
  let inBulletList = false;
  let inOrderedList = false;
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    if (line.startsWith('- ')) {
      if (!inBulletList) {
        processedLines.push('<ul>');
        inBulletList = true;
      }
      if (inOrderedList) {
        processedLines.push('</ol>');
        inOrderedList = false;
        processedLines.push('<ul>');
      }
      processedLines.push(`<li>${line.substring(2)}</li>`);
    } else if (/^\d+\. /.test(line)) {
      if (!inOrderedList) {
        processedLines.push('<ol>');
        inOrderedList = true;
      }
      if (inBulletList) {
        processedLines.push('</ul>');
        inBulletList = false;
        processedLines.push('<ol>');
      }
      processedLines.push(`<li>${line.replace(/^\d+\. /, '')}</li>`);
    } else {
      if (inBulletList) {
        processedLines.push('</ul>');
        inBulletList = false;
      }
      if (inOrderedList) {
        processedLines.push('</ol>');
        inOrderedList = false;
      }
      
      // Only add non-empty lines as paragraphs
      if (line.length > 0 && !line.startsWith('<h') && !line.startsWith('<pre>')) {
        processedLines.push(`<p>${line}</p>`);
      } else if (line.length > 0) {
        processedLines.push(line);
      }
    }
  }
  
  // Close any remaining lists
  if (inBulletList) processedLines.push('</ul>');
  if (inOrderedList) processedLines.push('</ol>');
  
  html = processedLines.join('');
  
  // Handle inline formatting
  html = html
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/(?<!\*)\*([^*]+)\*(?!\*)/g, '<em>$1</em>')
    .replace(/`([^`]+)`/g, '<code>$1</code>');
  
  return html;
};

/**
 * Convert HTML back to plain text for saving
 */
export const convertToPlainText = (html: string): string => {
  // First handle tables - convert HTML tables back to markdown
  let text = html;
  
  // Handle HTML tables
  text = text.replace(/<table[^>]*>([\s\S]*?)<\/table>/g, (match, tableContent) => {
    let markdownTable = '';
    
    // Extract header row from thead or first tr with th elements
    const theadMatch = tableContent.match(/<thead[^>]*>([\s\S]*?)<\/thead>/);
    let headerMatch;
    
    if (theadMatch) {
      headerMatch = theadMatch[1].match(/<tr[^>]*>\s*((?:<th[^>]*>[\s\S]*?<\/th>\s*)+)<\/tr>/);
    } else {
      // Look for header row in tbody or directly in table
      headerMatch = tableContent.match(/<tr[^>]*>\s*((?:<th[^>]*>[\s\S]*?<\/th>\s*)+)<\/tr>/);
    }
    
    if (headerMatch) {
      const headers = [...headerMatch[1].matchAll(/<th[^>]*>([\s\S]*?)<\/th>/g)]
        .map(match => {
          // Remove any p tags and get the text content
          const cellContent = match[1].replace(/<\/?p[^>]*>/g, '').trim();
          return cellContent;
        });
      
      markdownTable += '| ' + headers.join(' | ') + ' |\n';
      markdownTable += '|' + headers.map(() => '----------------------').join('|') + '|\n';
    }
    
    // Extract body rows from tbody or all tr elements with td
    const tbodyMatch = tableContent.match(/<tbody[^>]*>([\s\S]*?)<\/tbody>/);
    const bodyContent = tbodyMatch ? tbodyMatch[1] : tableContent;
    
    const bodyRows = [...bodyContent.matchAll(/<tr[^>]*>\s*((?:<td[^>]*>[\s\S]*?<\/td>\s*)+)<\/tr>/g)];
    bodyRows.forEach(rowMatch => {
      const cells = [...rowMatch[1].matchAll(/<td[^>]*>([\s\S]*?)<\/td>/g)]
        .map(match => {
          // Remove any p tags and get the text content, preserve empty cells
          const cellContent = match[1].replace(/<\/?p[^>]*>/g, '').trim();
          return cellContent; // Keep empty string if cell is empty
        });
      
      // Always output the row, preserving empty cells
      markdownTable += '| ' + cells.join(' | ') + ' |\n';
    });
    
    return markdownTable + '\n';
  });
  
  // Handle unordered lists
  text = text.replace(/<ul>([\s\S]*?)<\/ul>/g, (match, content) => {
    const items = content.match(/<li>(.*?)<\/li>/g) || [];
    return items.map(item => `- ${item.replace(/<\/?li>/g, '')}`).join('\n') + '\n';
  });
  
  // Handle ordered lists  
  text = text.replace(/<ol>([\s\S]*?)<\/ol>/g, (match, content) => {
    const items = content.match(/<li>(.*?)<\/li>/g) || [];
    return items.map((item, index) => `${index + 1}. ${item.replace(/<\/?li>/g, '')}`).join('\n') + '\n';
  });
  
  // Handle other elements
  text = text
    .replace(/<h1>(.*?)<\/h1>/g, '# $1\n\n')
    .replace(/<h2>(.*?)<\/h2>/g, '## $1\n\n') 
    .replace(/<h3>(.*?)<\/h3>/g, '### $1\n\n')
    .replace(/<strong>(.*?)<\/strong>/g, '**$1**')
    .replace(/<em>(.*?)<\/em>/g, '*$1*')
    .replace(/<code>(.*?)<\/code>/g, '`$1`')
    .replace(/<pre><code>([\s\S]*?)<\/code><\/pre>/g, '```\n$1\n```\n')
    .replace(/<blockquote>([\s\S]*?)<\/blockquote>/g, '> $1\n')
    .replace(/<p>(.*?)<\/p>/g, '$1\n\n')
    .replace(/<br\s*\/?>/g, '\n')
    .replace(/<\/?(?:div|span)[^>]*>/g, '')
    .replace(/<[^>]*>/g, '') // Remove any remaining HTML tags
    .replace(/\n{3,}/g, '\n\n') // Replace multiple newlines with double newlines
    .trim();
  
  return text;
};
