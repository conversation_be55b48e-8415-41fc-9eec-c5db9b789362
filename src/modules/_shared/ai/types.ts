// API Schema Types based on OpenAPI specification

export enum AgentCode {
  Master = 'orca-ai',
}

export enum ModelId {
  GPT4o = 'gpt-4o',
  GPT41 = 'gpt-4.1',
  GPT41Mini = 'gpt-4.1-mini'
}

export interface Agent {
  id: string
  name: string
  code: string
  role: string
  description?: string
  systemPrompt?: string
  additionalPrompt?: string
  modelId: string
  tools: string[]
  createdAt?: string
  updatedAt?: string
}

export interface AgentPromptUpdate extends Record<string, any> {
  agentCode: string
  additionalPrompt?: string
}

export interface Conversation {
  id: string
  title: string
  projectId: string
  userId: string
  promptTokens: number
  completionTokens: number
  totalCost?: number
  createdAt: string | Date
  updatedAt: string | Date
  messages: Message[]
}

export interface ConversationCreateRequest {
  projectId: string
}

export enum StreamMessageEvent {
  MessageCreated = 'MessageCreated',
  MessageDelta = 'MessageDelta',
  MessageError = 'MessageError',
  MessageComplete = 'MessageComplete',
  ConversationNameGenerated = 'ConversationNameGenerated',
}

export interface IMessageStep extends Record<string, any> {
  id: string
  name: string
  args: { [key: string]: any }
  result: string
  done: boolean
}

export interface Message {
  id: string
  content: string
  reasoningContent?: string
  steps?: IMessageStep[]
  step?: IMessageStep
  references?: { [key: string]: any }[]
  isBot: boolean
  isLoading: boolean
  promptTokens?: number
  completionTokens?: number
  userId?: string
  conversationId?: string
  createdAt: string | Date
  updatedAt: string | Date
}

export interface SendMessageRequest {
  conversationId: string
  content: string
  agentCode?: string
  sse: boolean
}

export interface UpdateMessageContentRequest {
  conversationId: string
  messageId: string
  content: string
}

export interface MessageEvent
  extends Partial<Message>,
    Partial<MessageEventError> {
  event: StreamMessageEvent
}

export interface MessageEventError {
  id: string
  event: StreamMessageEvent
  error: string
}

export enum MessageRole {
  user = 'user',
  assistant = 'assistant',
  system = 'system',
}

export interface AIAssistantState {
  isOpen: boolean
  isLoading: boolean
  currentConversation?: Conversation
  conversations: Conversation[]
  error?: string // Keep for backward compatibility
  isTyping: boolean
  suggestions: string[]
  // UI Panel States
  isAiPanelOpen: boolean
  activeTab: 'ai-chat' | 'settings' | null
  // Canvas Editor States
  isCanvasOnlyMode: boolean
  canvasContent: string
  currentEditingMessage: string | null // Track which message is being edited
  // Save Artifact Modal States
  isSaveArtifactModalOpen: boolean
  artifactList: ArtifactItem[]
}

export enum AIActionTypes {
  // UI Actions
  SET_CURRENT_CONVERSATION = 'SET_CURRENT_CONVERSATION',
  SET_CURRENT_CONVERSATION_MESSAGES = 'SET_CURRENT_CONVERSATION_MESSAGES',

  FETCH_LAST_CONVERSATION_OR_CREATE = 'FETCH_LAST_CONVERSATION_OR_CREATE',

  // Conversation Actions
  FETCH_CONVERSATIONS_REQUEST = 'FETCH_CONVERSATIONS_REQUEST',
  FETCH_CONVERSATIONS_SUCCESS = 'FETCH_CONVERSATIONS_SUCCESS',
  FETCH_CONVERSATIONS_FAILURE = 'FETCH_CONVERSATIONS_FAILURE',

  CREATE_CONVERSATION_REQUEST = 'CREATE_CONVERSATION_REQUEST',
  CREATE_CONVERSATION_SUCCESS = 'CREATE_CONVERSATION_SUCCESS',
  CREATE_CONVERSATION_FAILURE = 'CREATE_CONVERSATION_FAILURE',

  DELETE_CONVERSATION_REQUEST = 'DELETE_CONVERSATION_REQUEST',
  DELETE_CONVERSATION_SUCCESS = 'DELETE_CONVERSATION_SUCCESS',
  DELETE_CONVERSATION_FAILURE = 'DELETE_CONVERSATION_FAILURE',

  // Message Actions
  SEND_MESSAGE_REQUEST = 'SEND_MESSAGE_REQUEST',
  SEND_MESSAGE_SUCCESS = 'SEND_MESSAGE_SUCCESS',
  SEND_MESSAGE_FAILURE = 'SEND_MESSAGE_FAILURE',

  UPDATE_MESSAGE_CONTENT_REQUEST = 'UPDATE_MESSAGE_CONTENT_REQUEST',
  UPDATE_MESSAGE_CONTENT_SUCCESS = 'UPDATE_MESSAGE_CONTENT_SUCCESS',
  UPDATE_MESSAGE_CONTENT_FAILURE = 'UPDATE_MESSAGE_CONTENT_FAILURE',

  RECEIVE_AI_RESPONSE = 'RECEIVE_AI_RESPONSE',

  // Streaming Actions
  START_STREAMING_RESPONSE = 'START_STREAMING_RESPONSE',
  RECEIVE_STREAMING_CHUNK = 'RECEIVE_STREAMING_CHUNK',
  END_STREAMING_RESPONSE = 'END_STREAMING_RESPONSE',
  STREAMING_ERROR = 'STREAMING_ERROR',

  // Typing Actions
  SET_AI_TYPING = 'SET_AI_TYPING',

  // Suggestions Actions
  SET_SUGGESTIONS = 'SET_SUGGESTIONS',
  CLEAR_SUGGESTIONS = 'CLEAR_SUGGESTIONS',

  // Error Actions
  CLEAR_AI_ERROR = 'CLEAR_AI_ERROR',

  // Panel Management Actions
  TOGGLE_AI_CHAT_PANEL = 'TOGGLE_AI_CHAT_PANEL',
  TOGGLE_SETTINGS_PANEL = 'TOGGLE_SETTINGS_PANEL',
  SET_ACTIVE_TAB = 'SET_ACTIVE_TAB',
  CLOSE_ALL_PANELS = 'CLOSE_ALL_PANELS',
  ENTER_CANVAS_ONLY_MODE = 'ENTER_CANVAS_ONLY_MODE',
  EXIT_CANVAS_ONLY_MODE = 'EXIT_CANVAS_ONLY_MODE',
  SET_CURRENT_EDITING_MESSAGE = 'SET_CURRENT_EDITING_MESSAGE',
}
