@import '../../../../commons.less';
@import './right-menu.less';
@import './ai-assistant.less';

.ai-assistant-container {
  position: fixed;
  top: @navbar-height;
  right: 0;
  bottom: 0;
  width: 600px; // Increased width to accommodate tables better
  z-index: 1001;
  background: white;
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  border-left: 1px solid #e8e8e8;

  // When chat is also open, position AI assistant to the left of chat
  .chat-open & {
    right: 400px;

    @media (max-width: 1200px) {
      right: 350px;
      width: 450px; // Smaller on medium screens
    }

    @media (max-width: 768px) {
      right: 0;
      z-index: 1002; // Higher than chat on mobile
      width: 100%;
    }
  }

  @media (max-width: 1200px) {
    width: 450px; // Responsive width for medium screens
  }

  @media (max-width: 768px) {
    width: 100%;
    left: 0;
  }

  .ai-assistant-card {
    height: 100%;
    border: none;
    border-radius: 0;
    display: flex;
    flex-direction: column;

    .ant-card-body {
      padding: 0;
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  .ai-assistant-header {
    background: linear-gradient(135deg, #1890ff, #722ed1);
    padding: 12px 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e8e8e8;

    .header-left {
      display: flex;
      align-items: center;
      gap: 8px;

      .ai-icon {
        color: white;
        font-size: 18px;
      }

      .ant-typography {
        color: white !important;
        margin: 0;
      }
    }

    .header-right {
      display: flex;
      gap: 4px;
    }
  }

  .token-usage {
    padding: 12px 16px;
    background: #f8f9fa;
    border-bottom: 1px solid #e8e8e8;

    .ant-progress {
      margin: 0;
    }
  }

  .current-task {
    padding: 16px;
    background: white;
    border-bottom: 1px solid #e8e8e8;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .ant-typography {
      margin: 0;
      flex: 1;
      font-size: 16px;
      font-weight: 600;
      color: #262626;
    }

    .task-actions {
      display: flex;
      gap: 8px;
    }
  }

  .conversation-list {
    border-bottom: 1px solid #e8e8e8;
    max-height: 200px;
    overflow-y: auto;
    background: white;

    .conversation-list-header {
      padding: 12px 16px 8px;
      background: #fafafa;
      border-bottom: 1px solid #f0f0f0;
    }

    .conversation-items {
      .conversation-item {
        padding: 12px 16px;
        border-bottom: 1px solid #f5f5f5;
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        transition: background-color 0.2s;

        &:hover {
          background-color: #f5f5f5;
        }

        &.active {
          background-color: #e6f7ff;
          border-left: 3px solid #1890ff;
        }

        .conversation-info {
          flex: 1;
          min-width: 0;

          .conversation-title {
            display: block;
            font-weight: 500;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .conversation-time {
            font-size: 12px;
          }
        }

        .delete-conversation-btn {
          opacity: 0;
          transition: opacity 0.2s;
        }

        &:hover .delete-conversation-btn {
          opacity: 1;
        }
      }
    }
  }

  .ai-messages {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    overflow-x: hidden; // Prevent horizontal scroll on container, let content handle it
    background: #fafafa;
    max-height: calc(100vh - 300px);

    .empty-conversation {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      min-height: 300px;

      .empty-conversation-content {
        text-align: center;
        max-width: 300px;

        .empty-icon {
          font-size: 48px;
          color: #1890ff;
          margin-bottom: 16px;
        }

        h4 {
          color: #262626;
          margin-bottom: 8px;
        }

        .suggested-prompts {
          margin-top: 24px;
          text-align: left;

          ul {
            margin-top: 8px;
            padding-left: 16px;

            li {
              margin-bottom: 4px;
              color: #595959;
            }
          }
        }
      }
    }

    .ai-message {
      margin-bottom: 16px;

      .message-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 6px;

        .user-avatar {
          background: #1890ff;
        }

        .ai-avatar {
          background: #722ed1;
        }

        .message-meta {
          display: flex;
          flex-direction: column;
          gap: 2px;
          flex: 1;

          .agent-name {
            font-size: 12px;
            color: #722ed1;
            margin: 0;
          }

          .team-name {
            font-size: 10px;
            margin: 0;
          }

          .message-time {
            font-size: 11px;
            margin: 0;
          }
        }

        .ant-tag {
          margin: 0;
          font-size: 10px;
          padding: 0 4px;
          height: 18px;
          line-height: 16px;
        }
      }

      .message-content {
        margin-left: 32px;
        padding: 8px 12px;
        border-radius: 8px;
        word-wrap: break-word;
        overflow-x: auto; // Allow horizontal scrolling for wide content
        max-width: calc(100vw - 120px); // Expand to take more screen width

        .ant-typography {
          margin: 0;
          line-height: 1.5;
        }

        // Table styling for better display in chat messages
        table {
          width: 100% !important;
          min-width: 500px !important; // Ensure tables have minimum width
          border-collapse: collapse !important;
          margin: 8px 0 !important;
          font-size: 13px !important;
          background: white !important;
          border-radius: 4px !important;
          overflow: hidden !important;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;

          thead {
            background: #fafafa !important;

            th {
              padding: 8px 12px !important;
              border: 1px solid #d9d9d9 !important;
              border-bottom: 2px solid #d9d9d9 !important;
              font-weight: 600 !important;
              color: #262626 !important;
              text-align: left !important;
              background: #fafafa !important;
            }
          }

          tbody {
            tr {
              border-bottom: 1px solid #f0f0f0 !important; // Horizontal border between rows

              &:hover {
                background: #fafafa !important;
              }

              &:last-child {
                border-bottom: none !important;
              }

              td {
                padding: 8px 12px !important;
                border: 1px solid #f0f0f0 !important;
                border-left: 1px solid #d9d9d9 !important;
                border-right: 1px solid #d9d9d9 !important;
                color: #262626 !important;
                background: white !important;
                vertical-align: top !important;

                &:first-child {
                  border-left: 1px solid #d9d9d9 !important;
                }

                &:last-child {
                  border-right: 1px solid #d9d9d9 !important;
                }
              }
            }
          }

          // Handle markdown-rendered tables (from react-markdown)
          tr {
            border-bottom: 1px solid #f0f0f0 !important;

            &:hover {
              background: #fafafa !important;
            }

            &:last-child {
              border-bottom: none !important;
            }

            th {
              padding: 8px 12px !important;
              border: 1px solid #d9d9d9 !important;
              border-bottom: 2px solid #d9d9d9 !important;
              font-weight: 600 !important;
              color: #262626 !important;
              text-align: left !important;
              background: #fafafa !important;
            }

            td {
              padding: 8px 12px !important;
              border: 1px solid #f0f0f0 !important;
              border-left: 1px solid #d9d9d9 !important;
              border-right: 1px solid #d9d9d9 !important;
              color: #262626 !important;
              background: white !important;
              vertical-align: top !important;

              &:first-child {
                border-left: 1px solid #d9d9d9 !important;
              }

              &:last-child {
                border-right: 1px solid #d9d9d9 !important;
              }
            }
          }
        }

        // AI Message content with HTML rendering support
        .ai-message-content {
          line-height: 1.6;

          // Reset default margins for rendered HTML elements
          p {
            margin: 0 0 8px 0;
            line-height: 1.6;

            &:last-child {
              margin-bottom: 0;
            }
          }

          // Headings in AI responses
          h1,
          h2,
          h3,
          h4,
          h5,
          h6 {
            margin: 16px 0 8px 0;
            font-weight: 600;
            color: #262626;

            &:first-child {
              margin-top: 0;
            }
          }

          // Lists in AI responses
          ul,
          ol {
            margin: 8px 0;
            padding-left: 20px;

            li {
              margin: 2px 0;
            }
          }

          // Code blocks in AI responses
          pre {
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            padding: 12px;
            margin: 8px 0;
            overflow-x: auto;

            code {
              background: transparent;
              border: none;
              padding: 0;
            }
          }

          // Inline code in AI responses
          code {
            background: #f5f5f5;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            padding: 2px 4px;
            font-family: 'Monaco', 'Consolas', 'Lucida Console', monospace;
            font-size: 85%;
          }
        }
      }

      .message-tools {
        margin-left: 32px;
      }

      &.user-message {
        .message-content {
          background: #e6f7ff;
          border: 1px solid #91d5ff;
        }
      }

      &.assistant-message {
        .message-content {
          background: #f6ffed;
          border: 1px solid #b7eb8f;
        }
      }
    }
  }

  .quick-actions {
    padding: 8px 16px;
    background: white;
    border-bottom: 1px solid #e8e8e8;

    .ant-space {
      width: 100%;
    }

    .ant-btn {
      font-size: 12px;
      height: 24px;
      padding: 0 8px;
    }
  }

  .ai-input-area {
    background: white;
    border-top: 1px solid #e8e8e8;
    padding: 12px 16px;

    .selected-prompt {
      margin-bottom: 8px;

      .ant-tag {
        margin: 0;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;

        .anticon {
          margin-right: 4px;
        }
      }
    }

    .input-container {
      display: flex;
      align-items: flex-end;
      gap: 8px;

      .message-input {
        flex: 1;
        border-radius: 6px;
        border: 1px solid #d9d9d9;
        resize: none;

        &:focus {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        &::placeholder {
          color: #bfbfbf;
          font-size: 13px;
        }
      }

      .input-actions {
        display: flex;
        align-items: center;
        gap: 4px;

        .ant-btn {
          border: none;
          box-shadow: none;
          padding: 4px;
          height: 28px;
          width: 28px;
          display: flex;
          align-items: center;
          justify-content: center;

          &.ant-btn-primary {
            background: #1890ff;

            &:hover {
              background: #40a9ff;
            }

            &:disabled {
              background: #f5f5f5;
              color: #bfbfbf;
            }
          }

          .anticon {
            font-size: 14px;
          }
        }
      }
    }
  }



  // AI Assistant Toggle Button
  .ai-assistant-toggle-button {
    position: fixed;
    bottom: 90px; // Position above chat button
    right: 24px;
    width: 56px;
    height: 56px;
    border-radius: 50%;
    background: linear-gradient(135deg, #1890ff, #722ed1);
    color: white;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    z-index: 999;
    transition: all 0.3s ease;

    &:hover {
      background: linear-gradient(135deg, #40a9ff, #9254de);
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.95);
    }

    .anticon {
      color: white;
    }
  }

  // Responsive adjustments
  @media (max-width: 1200px) {
    .ai-assistant-container {
      width: 350px;
    }
  }

  @media (max-width: 768px) {
    .ai-assistant-container {
      width: 100%;
      left: 0;
    }

    .ai-assistant-toggle-button {
      bottom: 80px;
      right: 16px;
      width: 48px;
      height: 48px;
      font-size: 20px;
    }
  }

  // Animation for AI assistant panel
  .ai-assistant-slide-enter {
    transform: translateX(100%);
  }

  .ai-assistant-slide-enter-active {
    transform: translateX(0);
    transition: transform 300ms ease-in-out;
  }

  .ai-assistant-slide-exit {
    transform: translateX(0);
  }

  .ai-assistant-slide-exit-active {
    transform: translateX(100%);
    transition: transform 300ms ease-in-out;
  }

  // Scrollbar styling for messages
  .ai-messages::-webkit-scrollbar {
    width: 6px;
  }

  .ai-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .ai-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;

    &:hover {
      background: #a8a8a8;
    }
  }

  // Dropdown menu styling
  .ant-dropdown-menu {
    .ant-dropdown-menu-item {
      padding: 8px 12px;

      .ant-typography {
        margin: 0;
      }
    }
  }

  // Progress bar customization
  .token-usage .ant-progress-line {
    .ant-progress-bg {
      transition: all 0.3s ease;
    }
  }

  .message-container {
    margin-bottom: 1rem;

    .user-message {
      width: 90%;
      padding: .5rem;
      background-color: rgba(214, 213, 213, 0.3);
      border-radius: 1rem;
    }

    .bot-message {
      .message-step {
        background-color: rgba(246, 242, 242, 1);
        padding: .5rem;
        border-radius: 1rem;
        margin-bottom: .5rem;
        border: 1px solid;
      }
    }
  }
}



// Conversation History Modal
.conversation-history-modal {
  .ant-modal-header {
    background: linear-gradient(135deg, #1890ff, #722ed1);
    border-bottom: none;
    border-radius: 8px 8px 0 0;

    .ant-modal-title {
      color: white;
      font-weight: 600;

      .conversation-history-header {
        display: flex;
        align-items: center;
        gap: 8px;

        .header-icon {
          font-size: 16px;
        }
      }
    }
  }

  .ant-modal-body {
    padding: 0;
  }

  // Responsive design
  @media (max-width: 768px) {
    .ant-modal {
      width: 95% !important;
      margin: 10px auto;
    }
  }

  .conversation-modal-content {
    max-height: 500px;
    overflow-y: auto;

    .empty-description {
      color: #8c8c8c;
      line-height: 1.6;
    }

    .conversation-list {
      .conversation-item {
        padding: 16px 20px;
        border-bottom: 1px solid #f0f0f0;
        cursor: pointer;
        display: flex;
        align-items: flex-start;
        gap: 12px;
        transition: all 0.2s ease;
        position: relative;

        &:hover {
          background-color: #fafafa;

          .delete-conversation-btn {
            opacity: 1;
          }
        }

        &.active {
          background-color: #e6f7ff;
          border-left: 4px solid #1890ff;

          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: #1890ff;
          }
        }

        &:last-child {
          border-bottom: none;
        }

        .conversation-avatar {
          flex-shrink: 0;
          margin-top: 2px;
        }

        .conversation-content {
          flex: 1;
          min-width: 0;

          .conversation-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 6px;

            .conversation-title {
              font-size: 14px;
              font-weight: 500;
              color: #262626;
              line-height: 1.4;
              margin: 0;
              flex: 1;
              margin-right: 8px;

              // Truncate long titles
              display: -webkit-box;
              -webkit-line-clamp: 1;
              -webkit-box-orient: vertical;
              overflow: hidden;
            }

            .conversation-meta {
              flex-shrink: 0;

              .meta-icon {
                font-size: 12px;
                color: #8c8c8c;
              }

              .conversation-time {
                font-size: 12px;
                color: #8c8c8c;
                white-space: nowrap;
              }
            }
          }

          .conversation-preview {
            margin-bottom: 8px;

            .last-message {
              font-size: 13px;
              color: #595959;
              line-height: 1.4;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              -webkit-box-orient: vertical;
              overflow: hidden;
              margin: 0;
            }
          }

          .conversation-stats {
            .stat-item {
              display: flex;
              align-items: center;
              gap: 4px;

              .stat-icon {
                font-size: 12px;
                color: #8c8c8c;
              }

              .stat-text {
                font-size: 12px;
                color: #8c8c8c;
              }
            }

            .active-indicator {
              display: flex;
              align-items: center;
              gap: 4px;

              .active-icon {
                font-size: 12px;
                color: #52c41a;
              }

              .active-text {
                font-size: 12px;
                color: #52c41a;
                font-weight: 500;
              }
            }
          }
        }

        .conversation-actions {
          flex-shrink: 0;
          margin-top: 2px;

          .delete-conversation-btn {
            opacity: 0.6;
            transition: all 0.2s ease;

            &:hover {
              opacity: 1;
              background-color: #fff2f0;
              border-color: #ffccc7;
            }
          }
        }

        // Mobile responsive adjustments
        @media (max-width: 768px) {
          padding: 12px 16px;
          gap: 10px;

          .conversation-content {
            .conversation-header {
              .conversation-title {
                font-size: 13px;
              }

              .conversation-meta {
                .conversation-time {
                  font-size: 11px;
                }
              }
            }

            .conversation-preview {
              .last-message {
                font-size: 12px;
                -webkit-line-clamp: 1;
                line-clamp: 1;
              }
            }

            .conversation-stats {

              .stat-text,
              .active-text {
                font-size: 11px;
              }
            }
          }

          .conversation-actions {
            .delete-conversation-btn {
              opacity: 1; // Always visible on mobile
            }
          }
        }
      }
    }
  }
}