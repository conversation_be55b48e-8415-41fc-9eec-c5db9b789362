import React from 'react'
import { <PERSON><PERSON>, Typo<PERSON>, Button, Tooltip, Space, Avatar, Empty } from 'antd'
import {
  DeleteOutlined,
  MessageOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons'
import { Conversation } from '../../types'
import moment from 'moment'

const { Text, Title } = Typography

interface ConversationHistoryModalProps {
  visible: boolean
  onClose: () => void
  conversations: Conversation[]
  currentConversationId?: string
  onSelectConversation: (conversationId: string) => void
  onDeleteConversation: (conversationId: string) => void
  loading?: boolean
}

const ConversationHistoryModal: React.FC<ConversationHistoryModalProps> = ({
  visible,
  onClose,
  conversations,
  currentConversationId,
  onSelectConversation,
  onDeleteConversation,
  loading = false,
}) => {
  const formatDate = (date: string | Date) => {
    const momentDate = moment(date)
    const now = moment()

    if (momentDate.isSame(now, 'day')) {
      return momentDate.format('HH:mm')
    } else if (momentDate.isSame(now, 'week')) {
      return momentDate.format('ddd HH:mm')
    } else if (momentDate.isSame(now, 'year')) {
      return momentDate.format('MMM DD')
    } else {
      return momentDate.format('MMM DD, YYYY')
    }
  }

  const getMessageCount = (conversation: Conversation) => {
    return conversation.messages?.length || 0
  }

  return (
    <Modal
      title={
        <div className="conversation-history-header">
          <MessageOutlined className="header-icon" />
          <span>Conversation History</span>
        </div>
      }
      open={visible}
      onCancel={onClose}
      footer={null}
      width={700}
      className="conversation-history-modal"
      destroyOnClose
    >
      <div className="conversation-modal-content">
        {conversations.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <span className="empty-description">
                No conversations found
                <br />
                <Text type="secondary">Start a new conversation to see it here</Text>
              </span>
            }
          />
        ) : (
          <div className="conversation-list">
            {conversations.map((conversation) => {
              const isActive = conversation.id === currentConversationId
              const messageCount = getMessageCount(conversation)
              const formattedDate = formatDate(conversation.updatedAt)

              return (
                <div
                  key={conversation.id}
                  className={`conversation-item ${isActive ? 'active' : ''}`}
                  onClick={() => onSelectConversation(conversation.id)}
                >
                  <div className="conversation-avatar">
                    <Avatar
                      size={40}
                      style={{
                        backgroundColor: isActive ? '#1890ff' : '#f0f0f0',
                        color: isActive ? '#fff' : '#666',
                      }}
                    >
                      <MessageOutlined />
                    </Avatar>
                  </div>

                  <div className="conversation-content">
                    <div className="conversation-header">
                      <Text className="conversation-title" strong={isActive}>
                        {conversation.title}
                      </Text>
                      <div className="conversation-meta">
                        <Space size={4}>
                          <ClockCircleOutlined className="meta-icon" />
                          <Text type="secondary" className="conversation-time">
                            {formattedDate}
                          </Text>
                        </Space>
                      </div>
                    </div>

                    <div className="conversation-stats">
                      <Space size={12}>
                        <div className="stat-item">
                          <MessageOutlined className="stat-icon" />
                          <Text type="secondary" className="stat-text">
                            {messageCount} message{messageCount !== 1 ? 's' : ''}
                          </Text>
                        </div>
                        {isActive && (
                          <div className="active-indicator">
                            <CheckCircleOutlined className="active-icon" />
                            <Text type="secondary" className="active-text">
                              Active
                            </Text>
                          </div>
                        )}
                      </Space>
                    </div>
                  </div>

                  <div className="conversation-actions">
                    <Tooltip title="Delete conversation" placement="left">
                      <Button
                        type="text"
                        icon={<DeleteOutlined />}
                        size="small"
                        onClick={(e) => {
                          e.stopPropagation()
                          onDeleteConversation(conversation.id)
                        }}
                        className="delete-conversation-btn"
                        danger
                      />
                    </Tooltip>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>
    </Modal>
  )
}

export default ConversationHistoryModal
