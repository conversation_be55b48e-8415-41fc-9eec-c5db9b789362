import React, { useEffect, useRef, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import {
  Avatar,
  Button,
  Card,
  Input,
  Space,
  Spin,
  Tooltip,
  Typography,
} from 'antd'
import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  DeleteOutlined,
  HistoryOutlined,
  PaperClipOutlined,
  PlusOutlined,
  RobotOutlined,
  SendOutlined,
} from '@ant-design/icons'
import { AIAssistantState } from '../../types'
import {
  clearSuggestions,
  createConversationRequest,
  deleteConversationRequest,
  fetchConversationsRequest,
  fetchLastConversationOrCreate,
  sendMessageRequest,
  setCurrentConversation, setCurrentEditingMessage,
} from '../../actions'
import AppState from '../../../../../store/types'
import { extractProjectCode } from '../../../../../helper/share'
import intl from '../../../../../config/locale.config'
import { CanvasDialog } from '../canvas-dialog'
import { MessageContent } from './message'
import ConversationHistoryModal from './conversation-history-modal'

const { Text, Title } = Typography
const { TextArea } = Input

interface AIAssistantProps {
  isVisible: boolean
  onClose: () => void
  hideHeader?: boolean
}

export const AIChatBox: React.FC<AIAssistantProps> = ({
  isVisible,
  onClose,
  hideHeader = false,
}) => {
  const dispatch = useDispatch()
  const aiState = useSelector<AppState>(
    (state) => state?.aiAssistant
  ) as AIAssistantState
  const [messageInput, setMessageInput] = useState('')
  const [showConversationModal, setShowConversationModal] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const canvasRef = useRef<{ open: (initialContent: string) => void }>(null)

  useEffect(() => {
    if (isVisible) {
      dispatch(fetchConversationsRequest())
    }
  }, [isVisible, dispatch])

  useEffect(() => {
    // Create default conversation if none exists and we're visible
    if (isVisible && !aiState.currentConversation && !aiState.isLoading) {
      dispatch(fetchLastConversationOrCreate())
    }
  }, [isVisible, dispatch])

  useEffect(() => {
    const lastMessage = aiState.currentConversation?.messages?.slice(-1)?.[0]
    // Check if last conversation message is user's message
    if (
      lastMessage && !lastMessage.isBot
    ) {
      // If last message is user's, scroll to bottom
      scrollToBottom()
    }
  }, [aiState.currentConversation?.messages])

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  const handleSendMessage = () => {
    if (messageInput.trim() && aiState.currentConversation?.id) {
      const content = messageInput.trim()

      dispatch(
        sendMessageRequest({
          sse: true,
          content,
          conversationId: aiState.currentConversation.id,
        })
      )
      setMessageInput('')
      dispatch(clearSuggestions())
    }
  }

  const handleNewConversation = () => {
    dispatch(
      createConversationRequest({
        projectId: extractProjectCode() ?? '',
      })
    )
  }

  const handleSelectConversation = (conversationId: string) => {
    dispatch(setCurrentConversation(conversationId))
    setShowConversationModal(false)
  }

  const handleDeleteConversation = (conversationId: string) => {
    dispatch(deleteConversationRequest(conversationId))
  }

  const isLoading =
    aiState.isTyping ||
    aiState.currentConversation?.messages?.some((m) => m.isLoading)

  if (!isVisible) return null

  return (
    <div className="ai-assistant-container">
      <CanvasDialog
        ref={canvasRef}
        onClose={() => {
          // Clear the current editing message when canvas is closed
          dispatch(setCurrentEditingMessage(null))
        }}
      />
      <Card className="ai-assistant-card" bodyStyle={{ padding: 0 }}>
        {/* Current Task */}
        <div className="current-task">
          <Title level={5}>
            {aiState.currentConversation?.title ?? 'New Chat'}
          </Title>
          <div className="task-actions">
            <Tooltip title="New Conversation" placement="topRight">
              <Button
                type="text"
                icon={<PlusOutlined />}
                size="small"
                onClick={handleNewConversation}
              />
            </Tooltip>
            <Tooltip title="Conversation History" placement="topRight">
              <Button
                type="text"
                icon={<HistoryOutlined />}
                size="small"
                onClick={() => setShowConversationModal(true)}
              />
            </Tooltip>
          </div>
        </div>

        {/* Token Usage */}
        <div className="token-usage">
          <div className="d-flex justify-content-between">
            <Text strong>Token:</Text>
            <Space>
              <Tooltip
                title={intl.formatMessage({ id: 'ai.send-token.description' })}
              >
                <ArrowUpOutlined /> {aiState.currentConversation?.promptTokens}
              </Tooltip>
              <Tooltip
                title={intl.formatMessage({
                  id: 'ai.receive-token.description',
                })}
              >
                <ArrowDownOutlined />{' '}
                {aiState.currentConversation?.completionTokens}
              </Tooltip>
            </Space>
            <Text strong>{aiState.currentConversation?.totalCost} USD</Text>
          </div>
        </div>

        {/* Messages */}
        <div className="ai-messages">
          {aiState.currentConversation?.messages.length === 0 &&
            !aiState.isTyping ? (
            <div className="empty-conversation">
              <div className="empty-conversation-content">
                <RobotOutlined className="empty-icon" />
                <Title level={4}>
                  {intl.formatMessage({ id: 'ai.welcome-message.title' })}
                </Title>
                <Text type="secondary">
                  {intl.formatMessage({ id: 'ai.welcome-message.description' })}
                </Text>
              </div>
            </div>
          ) : (
            <>
              {aiState.currentConversation?.messages.map((message) => (
                <MessageContent
                  message={message}
                  openCanvas={() => {
                    dispatch(setCurrentEditingMessage(message.id))
                    canvasRef.current?.open(message.content)
                  }}
                />
              ))}
              {aiState.isTyping && (
                <>
                  <Text type="secondary">AI is thinking...</Text>{' '}
                  <Spin size="small" />
                </>
              )}
            </>
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* Quick Actions */}
        {aiState.suggestions.length > 0 && (
          <div className="quick-actions">
            <Text type="secondary" style={{ fontSize: '12px' }}>
              Suggestions:
            </Text>
            <Space wrap>
              {aiState.suggestions.map((suggestion, index) => (
                <Button
                  key={index}
                  size="small"
                  type="dashed"
                  onClick={() => setMessageInput(suggestion)}
                >
                  {suggestion}
                </Button>
              ))}
            </Space>
          </div>
        )}

        <div className="ai-input-area">
          <div className="input-container">
            <TextArea
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              placeholder={
                'Input a prompt, or type @ to call reference doc/ artefacts'
              }
              autoSize={{ minRows: 1, maxRows: 4 }}
              onPressEnter={(e) => {
                if (!e.shiftKey) {
                  e.preventDefault()
                  handleSendMessage()
                }
              }}
              className="message-input"
            />
            <div className="input-actions">
              <Button type="text" icon={<PaperClipOutlined />} size="small" />
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={handleSendMessage}
                disabled={!messageInput.trim() || isLoading}
                size="small"
              />
            </div>
          </div>
        </div>

        {/* Conversation History Modal */}
        <ConversationHistoryModal
          visible={showConversationModal}
          onClose={() => setShowConversationModal(false)}
          conversations={aiState.conversations}
          currentConversationId={aiState.currentConversation?.id}
          onSelectConversation={handleSelectConversation}
          onDeleteConversation={handleDeleteConversation}
          loading={aiState.isLoading}
        />
      </Card>
    </div>
  )
}
