Index: .env
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>REACT_APP_API_LOGIN = \"http://localhost:3000\"\nREACT_APP_API_BACKEND = \"https://ba-vista-backend.ops-ai.dev/api/\"\nREACT_APP_API_AI = \"https://ba-vista-ai.ops-ai.dev/v1\"\n#REACT_APP_API_AI = \"http://localhost:8080/v1\"\nREACT_APP_API_UPLOAD_IMG =\"https://ba-vista-backend.ops-ai.dev/api/projects\"\nREACT_APP_API_TEMPLATE_USECASE = \"https://ba-vista-backend.ops-ai.dev/api/importtemplates/function\"\nREACT_APP_API_TEMPLATE_USERREQUIREMENT = \"https://ba-vista-backend.ops-ai.dev/api/importtemplates/userrequirement\"\nREACT_APP_CLIENT_ID = '4cc7fef8-50e9-47dd-8418-47dd7d837f97'\nREACT_APP_AUTHORITY = 'https://login.microsoftonline.com/1c3cfd54-42ff-43ab-9d54-20d83dea2411'\nREACT_APP_SCOPES = 'api://74098fea-63bb-444b-81cc-a4bf714cb4ca/access_as_user'\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/.env b/.env
--- a/.env	(revision 31a27ff786887d6e1dbbd3b50eae4a132c278b65)
+++ b/.env	(date 1752084540744)
@@ -1,7 +1,7 @@
 REACT_APP_API_LOGIN = "http://localhost:3000"
 REACT_APP_API_BACKEND = "https://ba-vista-backend.ops-ai.dev/api/"
-REACT_APP_API_AI = "https://ba-vista-ai.ops-ai.dev/v1"
-#REACT_APP_API_AI = "http://localhost:8080/v1"
+#REACT_APP_API_AI = "https://ba-vista-ai.ops-ai.dev/v1"
+REACT_APP_API_AI = "http://localhost:8080/v1"
 REACT_APP_API_UPLOAD_IMG ="https://ba-vista-backend.ops-ai.dev/api/projects"
 REACT_APP_API_TEMPLATE_USECASE = "https://ba-vista-backend.ops-ai.dev/api/importtemplates/function"
 REACT_APP_API_TEMPLATE_USERREQUIREMENT = "https://ba-vista-backend.ops-ai.dev/api/importtemplates/userrequirement"
