<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="2285c48c-54f4-47db-837a-bc74d3e04487" name="Changes" comment="Enhance message scrolling logic to only scroll to bottom if the last message is from the user">
      <change beforePath="$PROJECT_DIR$/.env" beforeDir="false" afterPath="$PROJECT_DIR$/.env" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="module" />
      <option value="repository" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="feature/demo" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="UPDATE_TYPE" value="REBASE" />
  </component>
  <component name="MacroExpansionManager">
    <option name="directoryName" value="jSU4MK0w" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2z4yGO1ZGdFoz4KuC2GggKV5usb" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="1" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ModuleVcsDetector.initialDetectionPerformed&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.formatter.settings.were.checked&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.migrated.go.modules.settings&quot;: &quot;true&quot;,
    &quot;database.data.extractors.current.export.id&quot;: &quot;Semicolon-separated_id&quot;,
    &quot;database.data.extractors.current.id&quot;: &quot;Semicolon-separated_id&quot;,
    &quot;git-widget-placeholder&quot;: &quot;dev&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;junie.onboarding.icon.badge.shown&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;/Volumes/Data/Projects/ba-vista-frontend/src/assets/icons&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;org.rust.first.attach.projects&quot;: &quot;true&quot;,
    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;/Volumes/Data/Projects/ba-vista-frontend/node_modules/prettier&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.2&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;com.augmentcode.intellij.settings&quot;,
    &quot;ts.external.directory.path&quot;: &quot;/Volumes/Data/Projects/ba-vista-frontend/node_modules/typescript/lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mongo&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/src/assets/icons" />
      <recent name="$PROJECT_DIR$/src/modules/_shared/ai/components/chatbox" />
      <recent name="$PROJECT_DIR$" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/src/modules/_shared/ai/components" />
      <recent name="$PROJECT_DIR$/src/modules/_shared/ai/components/chatbox" />
      <recent name="$PROJECT_DIR$/src/modules/_shared/ai/components/chat-box" />
    </key>
  </component>
  <component name="RunManager">
    <configuration default="true" type="GoApplicationRunConfiguration" factoryName="Go Application">
      <module name="ba-vista-frontend" />
      <working_directory value="$PROJECT_DIR$" />
      <go_parameters value="-i" />
      <kind value="FILE" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="GoTestRunConfiguration" factoryName="Go Test">
      <module name="ba-vista-frontend" />
      <working_directory value="$PROJECT_DIR$" />
      <go_parameters value="-i" />
      <kind value="DIRECTORY" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$" />
      <framework value="gotest" />
      <method v="2" />
    </configuration>
  </component>
  <component name="RustProjectSettings">
    <option name="toolchainHomeDirectory" value="$USER_HOME$/.cargo/bin" />
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.26927.53" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-IU-251.26927.53" />
      </set>
    </attachedChunks>
  </component>
  <component name="ShelveChangesManager">
    <option name="groupingKeys">
      <option value="directory" />
      <option value="module" />
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2285c48c-54f4-47db-837a-bc74d3e04487" name="Changes" comment="" />
      <created>1751004543710</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751004543710</updated>
      <workItem from="1751004544805" duration="5122000" />
      <workItem from="1751325671367" duration="1488000" />
      <workItem from="1751331384296" duration="2542000" />
      <workItem from="1751346155143" duration="881000" />
      <workItem from="1751351797642" duration="3364000" />
      <workItem from="1751366016904" duration="29669000" />
      <workItem from="1751538738115" duration="18709000" />
      <workItem from="1751588981586" duration="28714000" />
      <workItem from="1752074625980" duration="1888000" />
      <workItem from="1752083900830" duration="13925000" />
      <workItem from="1752167959417" duration="696000" />
    </task>
    <task id="LOCAL-00001" summary="Feature: enhance AI Assistant with new canvas editor, update API integration, and improve message handling">
      <option name="closed" value="true" />
      <created>1751540353401</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751540353401</updated>
    </task>
    <task id="LOCAL-00002" summary="Feature: refactor chatbox and actions to use updated Message type, improve message handling and conversation management">
      <option name="closed" value="true" />
      <created>1751584739683</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1751584739683</updated>
    </task>
    <task id="LOCAL-00003" summary="Feature: refactor agent instructions handling, update API service integration, and enhance state management">
      <option name="closed" value="true" />
      <created>1751588258221</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1751588258221</updated>
    </task>
    <task id="LOCAL-00004" summary="Feature: refactor agent instructions handling, update API service integration, and enhance state management">
      <option name="closed" value="true" />
      <created>1751928328563</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1751928328563</updated>
    </task>
    <task id="LOCAL-00005" summary="Feature: refactor agent instructions handling, update API service integration, and enhance state management">
      <option name="closed" value="true" />
      <created>1751928384460</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1751928384460</updated>
    </task>
    <task id="LOCAL-00006" summary="Refactor AI assistant components and enhance message handling&#10;&#10;- Remove unused settings panel export from index.ts&#10;- Add Bootstrap and jQuery scripts to index.html&#10;- Introduce new SVG icons for delegation, thinking, and analysis&#10;- Update message handling in reducer to support message steps&#10;- Create MessageContent and MessageStep components for better message rendering&#10;- Adjust styles for message display and interaction">
      <option name="closed" value="true" />
      <created>1751960519921</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1751960519921</updated>
    </task>
    <task id="LOCAL-00007" summary="Refactor AI assistant components and enhance message handling&#10;&#10;- Remove unused settings panel export from index.ts&#10;- Add Bootstrap and jQuery scripts to index.html&#10;- Introduce new SVG icons for delegation, thinking, and analysis&#10;- Update message handling in reducer to support message steps&#10;- Create MessageContent and MessageStep components for better message rendering&#10;- Adjust styles for message display and interaction">
      <option name="closed" value="true" />
      <created>1751962073530</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1751962073530</updated>
    </task>
    <task id="LOCAL-00008" summary="Enhance Azure Pipelines configuration to include ArgoCD image tag update stage">
      <option name="closed" value="true" />
      <created>1752083953717</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1752083953717</updated>
    </task>
    <task id="LOCAL-00009" summary="Feature: enhance AI assistant with conversation history modal and improve heading levels">
      <option name="closed" value="true" />
      <created>1752099149474</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1752099149474</updated>
    </task>
    <task id="LOCAL-00010" summary="Enhance message scrolling logic to only scroll to bottom if the last message is from the user">
      <option name="closed" value="true" />
      <created>1752168527722</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1752168527722</updated>
    </task>
    <option name="localTasksCounter" value="11" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Feature: enhance AI Assistant with new canvas editor, update API integration, and improve message handling" />
    <MESSAGE value="Feature: refactor chatbox and actions to use updated Message type, improve message handling and conversation management" />
    <MESSAGE value="Feature: refactor agent instructions handling, update API service integration, and enhance state management" />
    <MESSAGE value="Refactor AI assistant components and enhance message handling&#10;&#10;- Remove unused settings panel export from index.ts&#10;- Add Bootstrap and jQuery scripts to index.html&#10;- Introduce new SVG icons for delegation, thinking, and analysis&#10;- Update message handling in reducer to support message steps&#10;- Create MessageContent and MessageStep components for better message rendering&#10;- Adjust styles for message display and interaction" />
    <MESSAGE value="Enhance Azure Pipelines configuration to include ArgoCD image tag update stage" />
    <MESSAGE value="Feature: enhance AI assistant with conversation history modal and improve heading levels" />
    <MESSAGE value="Enhance message scrolling logic to only scroll to bottom if the last message is from the user" />
    <option name="LAST_COMMIT_MESSAGE" value="Enhance message scrolling logic to only scroll to bottom if the last message is from the user" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>